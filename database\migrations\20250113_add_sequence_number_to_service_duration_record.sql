-- 为服务时长记录表添加序号字段，支持同一服务的多次记录
-- 创建时间: 2025-01-13

-- 添加序号字段
ALTER TABLE service_duration_records 
ADD COLUMN sequence_number INT NOT NULL DEFAULT 1 
COMMENT '序号（用于区分同一服务的多次记录，从1开始）';

-- 为现有记录设置序号为1（保持向后兼容）
UPDATE service_duration_records SET sequence_number = 1 WHERE sequence_number IS NULL;

-- 添加索引以提高查询性能
-- 主服务的复合索引
CREATE INDEX idx_service_duration_main_service 
ON service_duration_records (order_id, employee_id, record_type, order_detail_id, service_id, sequence_number)
WHERE record_type = 'main_service';

-- 追加服务中增项服务的复合索引
CREATE INDEX idx_service_duration_additional_service_order 
ON service_duration_records (order_id, employee_id, record_type, additional_service_order_id, additional_service_id, sequence_number)
WHERE record_type = 'additional_service' AND additional_service_order_id IS NOT NULL;

-- 主订单中增项服务的复合索引
CREATE INDEX idx_service_duration_additional_service_main 
ON service_duration_records (order_id, employee_id, record_type, order_detail_id, additional_service_id, sequence_number)
WHERE record_type = 'additional_service' AND additional_service_order_id IS NULL;
