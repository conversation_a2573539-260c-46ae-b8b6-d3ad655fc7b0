# 追加服务多次记录功能测试用例

## 测试场景

### 场景1：用户下单包含多个相同增项服务
1. 用户创建订单，包含：
   - 主服务：洗护服务 x1
   - 增项服务：指甲修剪 x3

### 场景2：员工为同一增项服务分别计时

#### 测试步骤：

1. **获取下一个序号**
   ```http
   GET /employee/service-duration/next-sequence?orderId=123&recordType=additional_service&additionalServiceOrderId=456&additionalServiceId=789
   
   期望返回: { "nextSequenceNumber": 1 }
   ```

2. **开始第1次服务**
   ```http
   POST /employee/service-duration/start
   {
     "orderId": 123,
     "additionalServiceOrderId": 456,
     "recordType": "additional_service",
     "additionalServiceId": 789,
     "sequenceNumber": 1,
     "remark": "第1次指甲修剪"
   }
   
   期望：成功创建记录，sequenceNumber=1
   ```

3. **结束第1次服务**
   ```http
   POST /employee/service-duration/end
   {
     "recordId": [从步骤2返回的记录ID],
     "remark": "第1次完成"
   }
   
   期望：成功结束，记录时长
   ```

4. **获取下一个序号（应该是2）**
   ```http
   GET /employee/service-duration/next-sequence?orderId=123&recordType=additional_service&additionalServiceOrderId=456&additionalServiceId=789
   
   期望返回: { "nextSequenceNumber": 2 }
   ```

5. **开始第2次服务**
   ```http
   POST /employee/service-duration/start
   {
     "orderId": 123,
     "additionalServiceOrderId": 456,
     "recordType": "additional_service",
     "additionalServiceId": 789,
     "sequenceNumber": 2,
     "remark": "第2次指甲修剪"
   }
   
   期望：成功创建记录，sequenceNumber=2
   ```

6. **查询服务记录**
   ```http
   GET /employee/service-duration/records/123
   
   期望：返回两条记录，按序号排序
   ```

### 场景3：验证重复开始保护

1. **尝试重复开始相同序号的服务**
   ```http
   POST /employee/service-duration/start
   {
     "orderId": 123,
     "additionalServiceOrderId": 456,
     "recordType": "additional_service",
     "additionalServiceId": 789,
     "sequenceNumber": 1,
     "remark": "重复开始"
   }
   
   期望：返回错误，提示该序号的服务已开始
   ```

## 验证点

1. ✅ 序号字段正确添加到数据库
2. ✅ 可以为同一服务创建多条记录
3. ✅ 序号自动递增
4. ✅ 重复开始保护机制有效
5. ✅ 查询结果按序号排序
6. ✅ 时长统计包含所有序号的记录
7. ✅ 向后兼容性（现有记录序号默认为1）

## 数据库验证

执行以下SQL验证数据结构：
```sql
-- 检查字段是否添加成功
DESCRIBE service_duration_records;

-- 检查现有记录的序号
SELECT id, sequence_number, service_name, additional_service_name 
FROM service_duration_records 
ORDER BY created_at DESC 
LIMIT 10;

-- 检查索引是否创建成功
SHOW INDEX FROM service_duration_records;
```
