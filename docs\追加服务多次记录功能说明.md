# 追加服务多次记录功能说明

## 功能概述

为了支持用户对同一个增项服务下多个数量时的时长统计需求，系统新增了序号机制，允许为同一服务创建多条独立的时长记录。

## 主要改进

### 1. 数据库层面
- 在 `service_duration_records` 表中添加 `sequence_number` 字段
- 序号从1开始，用于区分同一服务的多次记录
- 添加相应的数据库索引以提高查询性能

### 2. API接口改进

#### 新增接口
- `GET /employee/service-duration/next-sequence` - 获取下一个可用的序号

#### 修改接口
- `POST /employee/service-duration/start` - 开始服务接口新增 `sequenceNumber` 参数

### 3. 使用场景

#### 场景1：用户下单时选择同一增项服务多个数量
```
用户订单：
- 主服务：洗护服务 x1
- 增项服务：指甲修剪 x3
```

#### 场景2：员工为同一增项服务分别计时
```
员工操作流程：
1. 开始第1次指甲修剪服务 (sequenceNumber: 1)
2. 结束第1次指甲修剪服务
3. 开始第2次指甲修剪服务 (sequenceNumber: 2)
4. 结束第2次指甲修剪服务
5. 开始第3次指甲修剪服务 (sequenceNumber: 3)
6. 结束第3次指甲修剪服务
```

## API使用示例

### 1. 获取下一个序号
```http
GET /employee/service-duration/next-sequence?orderId=123&recordType=additional_service&additionalServiceOrderId=456&additionalServiceId=789

Response:
{
  "nextSequenceNumber": 2
}
```

### 2. 开始服务（指定序号）
```http
POST /employee/service-duration/start
{
  "orderId": 123,
  "additionalServiceOrderId": 456,
  "recordType": "additional_service",
  "additionalServiceId": 789,
  "sequenceNumber": 2,
  "remark": "第2次指甲修剪"
}
```

### 3. 查询服务记录
查询结果将按开始时间和序号排序，便于区分不同次数的服务记录。

## 向后兼容性

- 现有记录的序号默认设置为1
- 如果不指定序号，默认使用序号1
- 现有API调用方式保持不变

## 注意事项

1. 序号必须是正整数，从1开始
2. 同一服务的不同序号记录是独立的，可以分别开始和结束
3. 时长统计会考虑所有序号的记录
4. 平均时长计算基于所有完成的记录（包括不同序号）

## 数据库迁移

执行以下SQL文件进行数据库升级：
```
database/migrations/20250113_add_sequence_number_to_service_duration_record.sql
```
